<?php

namespace App\Repositories\API;

use App\Exceptions\ExceptionHandler;
use App\Models\Business;
use Exception;
use Illuminate\Support\Facades\DB;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Eloquent\BaseRepository;
use App\Services\ZipCode\ZipCode;
use Illuminate\Support\Facades\Log;

class BusinessRepository extends BaseRepository
{
    private $zipCodeDistances = [];

    public function model()
    {
        return Business::class;
    }

    public function boot()
    {
        try {
            $this->pushCriteria(app(RequestCriteria::class));
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    private function sanitizeString(?string $value): ?string
    {
        if (!$value) return null;

        $value = preg_replace('/[\x{200B}-\x{200D}\x{FEFF}\x{202F}\x{FFFD}]/u', '', $value);
        $value = preg_replace('/[^\P{C}\n]+/u', '', $value);
        $value = trim($value);

        return $value;
    }

    public function importBusinesses(array $businesses)
    {
        DB::beginTransaction();
        try {
            $imported = 0;
            $updated = 0;
            $skipped = 0;

            foreach ($businesses as $businessData) {
                if (empty($businessData['phone'])) {
                    $skipped++;
                    continue;
                }

                $hours = [];
                if (is_array($businessData['hours'])) {
                    foreach ($businessData['hours'] as $schedule) {
                        if (preg_match('/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)(.*)$/i', $schedule, $matches)) {
                            $day = strtolower($matches[1]);
                            $time = trim($matches[2]);
                            $hours[$day] = $time;
                        }
                    }
                }

                $existingBusiness = $this->model
                    ->where('name', $businessData['name'])
                    ->where('address', $businessData['address'])
                    ->first();

                $data = [
                    'name'     => $this->sanitizeString($businessData['name'] ?? null),
                    'category' => $this->sanitizeString($businessData['category'] ?? null),
                    'location' => $this->sanitizeString($businessData['location'] ?? null),
                    'address'  => $this->sanitizeString($businessData['address'] ?? null),
                    'phone'    => $this->sanitizeString($businessData['phone'] ?? null),
                    'website'  => $this->sanitizeString($businessData['website'] ?? null),
                    'email'    => $this->sanitizeString($businessData['email'] ?? null),
                    'hours'    => array_map(fn($h) => $this->sanitizeString($h), $businessData['hours'] ?? []),
                    'photos'   => $businessData['photos'] ?? [],
                    'services' => $businessData['services'] ?? [],
                    'reviews'  => $businessData['reviews'] ?? [],
                    'lat'      => isset($businessData['lat']) ? $businessData['lat'] : null,
                    'lng'      => isset($businessData['lng']) ? $businessData['lng'] : null,
                ];

                if ($existingBusiness) {
                    // If category is different, append it to existing categories
                    if ($existingBusiness->category != $data['category']) {
                        $existingCategories = explode(',', $existingBusiness->category);
                        if (!in_array($data['category'], $existingCategories)) {
                            $data['category'] = $existingBusiness->category . ',' . $data['category'];
                            $existingBusiness->update(['category' => $data['category']]);
                            $updated++;
                        } else {
                            $skipped++;
                        }
                        continue;
                    }
                    $skipped++;


                } else {
                    $this->model->create($data);
                    $imported++;
                }
            }

            DB::commit();
            return [
                'imported' => $imported,
                'updated' => $updated,
                'skipped' => $skipped
            ];
        } catch (Exception $e) {
            DB::rollback();
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function getByUuid(string $businessUuid)
    {
        try {
            return $this->model
                ->where('business_uuid', $businessUuid)
                ->first();
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function getAll(array $filters = [], ?int $perPage = 15)
    {
        try {
            $query = $this->model->newQuery();
            
            if (!empty($filters['category'])) {
                $categories = is_array($filters['category']) ? $filters['category'] : [$filters['category']];
                $query->whereIn('category', $categories);
            }

            if (!empty($filters['zip_code'])) {
                $zipCode = $filters['zip_code'];
                $radius = $filters['radius'] ?? 50; // Default to 50 miles if not specified

                try {
                    $zipService = new ZipCode();
                    $zipCodesWithDistance = $zipService->getZipCodesInRadiusWithDistance($zipCode, $radius);

                    if (empty($zipCodesWithDistance)) {
                        \Log::warning('No zip codes found in radius for zip code: ' . $zipCode, [
                            'zip_code' => $zipCode,
                            'radius_miles' => $radius
                        ]);
                        // Fallback to exact zip code match instead of returning all businesses
                        $query->where('zip_code', $zipCode);
                    } else {
                        \Log::debug('Found zip codes in radius for business search', [
                            'origin_zip' => $zipCode,
                            'radius_miles' => $radius,
                            'found_count' => count($zipCodesWithDistance)
                        ]);

                        $zipCodes = array_keys($zipCodesWithDistance);

                        // Use the generated zip_code column with index and maintain distance order
                        $query->whereIn('zip_code', $zipCodes)
                              ->orderByRaw('FIELD(zip_code,' . implode(',', array_map(fn($zip) => "'$zip'", $zipCodes)) . ')');

                        // Store distance mapping for later use
                        $this->zipCodeDistances = $zipCodesWithDistance;
                    }

                } catch (\Exception $e) {
                    \Log::error('ZipCodeService error for zip code ' . $zipCode . ': ' . $e->getMessage(), [
                        'exception' => $e,
                        'filters' => $filters,
                        'radius' => $radius
                    ]);

                    // Fallback to exact zip code match
                    $query->where('zip_code', $zipCode);
                }
            }
            
            if (!empty($filters['location'])) {
                $query->where('location', 'like', '%' . $filters['location'] . '%');
            }
            
            if (!empty($filters['search'])) {
                $search = $filters['search'];
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', '%' . $search . '%')
                      ->orWhere('category', 'like', '%' . $search . '%')
                      ->orWhere('location', 'like', '%' . $search . '%')
                      ->orWhere('address', 'like', '%' . $search . '%')
                      ->orWhere('phone', 'like', '%' . $search . '%')
                      ->orWhere('email', 'like', '%' . $search . '%');
                });
            }

            if (empty($filters['zip_code'])) {
                $query->orderBy('created_at', $filters['sort_direction'] ?? 'desc');
            }

            $results = $perPage ? $query->paginate($perPage) : $query->get();

            // Add distance information if zipcode search was performed
            if (!empty($filters['zip_code']) && !empty($this->zipCodeDistances)) {
                if ($perPage) {
                    // For paginated results
                    $results->getCollection()->transform(function ($business) {
                        if ($business->zip_code && isset($this->zipCodeDistances[$business->zip_code])) {
                            $business->distance_miles = round($this->zipCodeDistances[$business->zip_code], 2);
                        }
                        return $business;
                    });
                } else {
                    // For non-paginated results (Collection)
                    $results->transform(function ($business) {
                        if ($business->zip_code && isset($this->zipCodeDistances[$business->zip_code])) {
                            $business->distance_miles = round($this->zipCodeDistances[$business->zip_code], 2);
                        }
                        return $business;
                    });
                }
            }

            return $results;
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function getUniqueCategories()
    {
        try {
            return $this->model
                ->select('category')
                ->distinct()
                ->orderBy('category')
                ->pluck('category')
                ->map(function($category) {
                    return [
                        'value' => $category,
                        'label' => ucfirst($category),
                        'count' => $this->model->where('category', $category)->count()
                    ];
                })
                ->values()
                ->all();
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }
}