<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\API\BusinessController;
use Illuminate\Http\Request;

class TestBusinessAPI extends Command
{
    protected $signature = 'test:business-api {zipcode} {--radius=50}';
    protected $description = 'Test the Business API controller directly';

    public function handle()
    {
        $zipcode = $this->argument('zipcode');
        $radius = $this->option('radius');
        
        $this->info("=== Testing Business API Controller ===");
        $this->info("ZipCode: {$zipcode}");
        $this->info("Radius: {$radius} miles");
        $this->info("");

        // Create a mock request
        $request = new Request([
            'zip_code' => $zipcode,
            'radius' => $radius,
            'per_page' => 10
        ]);

        try {
            $controller = app(BusinessController::class);
            $response = $controller->index($request);
            
            $responseData = $response->getData(true);
            
            if ($responseData['success']) {
                $businesses = $responseData['data'];
                $this->info("API Response Success: Found " . count($businesses) . " businesses");
                $this->info("Filters applied: " . json_encode($responseData['meta']['filters']));
                $this->info("");

                if (count($businesses) > 0) {
                    $tableData = [];
                    foreach ($businesses as $business) {
                        $tableData[] = [
                            'Name' => substr($business['name'], 0, 30),
                            'ZipCode' => $business['zip_code'] ?? 'N/A',
                            'Distance' => isset($business['distance_miles']) ? $business['distance_miles'] . ' mi' : 'N/A',
                            'Location' => substr($business['location'] ?? '', 0, 20),
                        ];
                    }

                    $this->table(
                        ['Name', 'ZipCode', 'Distance', 'Location'],
                        $tableData
                    );

                    // Check for the problematic zipcode
                    $problematicBusiness = collect($businesses)->firstWhere('zip_code', '38632');
                    if ($problematicBusiness) {
                        $this->warn("");
                        $this->warn("Found business in zipcode 38632:");
                        $this->warn("Name: " . $problematicBusiness['name']);
                        $this->warn("Distance: " . ($problematicBusiness['distance_miles'] ?? 'N/A') . " miles");
                        $this->warn("This business is " . ($problematicBusiness['distance_miles'] <= $radius ? "WITHIN" : "OUTSIDE") . " the {$radius}-mile radius");
                    } else {
                        $this->info("✓ No businesses found in zipcode 38632 within {$radius}-mile radius");
                    }
                } else {
                    $this->warn("No businesses found within {$radius} miles of {$zipcode}");
                }
            } else {
                $this->error("API Response Error: " . json_encode($responseData));
            }

        } catch (\Exception $e) {
            $this->error("Error during API test: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }
}
