<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Repositories\API\BusinessRepository;

class TestZipCodeBusinessSearch extends Command
{
    protected $signature = 'test:zipcode-business-search {zipcode} {--radius=50}';
    protected $description = 'Test zipcode-based business search with configurable radius';

    public function handle()
    {
        $zipcode = $this->argument('zipcode');
        $radius = $this->option('radius');
        
        $this->info("=== Testing Business Search by ZipCode ===");
        $this->info("ZipCode: {$zipcode}");
        $this->info("Radius: {$radius} miles");
        $this->info("");

        $repository = app(BusinessRepository::class);
        
        $filters = [
            'zip_code' => $zipcode,
            'radius' => $radius
        ];

        try {
            $businesses = $repository->getAll($filters, null); // Get all results, no pagination
            
            $this->info("Found " . $businesses->count() . " businesses within {$radius} miles of {$zipcode}:");
            $this->info("");

            if ($businesses->count() > 0) {
                $tableData = [];
                foreach ($businesses as $business) {
                    $tableData[] = [
                        'ID' => $business->id,
                        'Name' => substr($business->name, 0, 30),
                        'ZipCode' => $business->zip_code,
                        'Distance' => isset($business->distance_miles) ? $business->distance_miles . ' mi' : 'N/A',
                        'Location' => substr($business->location, 0, 20),
                        'Address' => substr($business->address, 0, 30)
                    ];
                }

                $this->table(
                    ['ID', 'Name', 'ZipCode', 'Distance', 'Location', 'Address'],
                    $tableData
                );

                // Show specific info about the problematic zipcode if found
                $problematicBusiness = $businesses->where('zip_code', '38632')->first();
                if ($problematicBusiness) {
                    $this->warn("");
                    $this->warn("Found business in zipcode 38632:");
                    $this->warn("Name: " . $problematicBusiness->name);
                    $this->warn("Distance: " . ($problematicBusiness->distance_miles ?? 'N/A') . " miles");
                    $this->warn("Location: " . $problematicBusiness->location);
                    $this->warn("Address: " . $problematicBusiness->address);
                }
            } else {
                $this->warn("No businesses found within {$radius} miles of {$zipcode}");
            }

        } catch (\Exception $e) {
            $this->error("Error during search: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
