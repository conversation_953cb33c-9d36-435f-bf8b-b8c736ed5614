<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ZipCode\ZipCode;
use App\Models\ZipCode as ZipCodeModel;

class DebugZipCodeDistance extends Command
{
    protected $signature = 'debug:zipcode-distance {origin} {target?}';
    protected $description = 'Debug zipcode distance calculations';

    public function handle()
    {
        $originZip = $this->argument('origin');
        $targetZip = $this->argument('target');
        
        $zipService = new ZipCode();

        $this->info("=== ZipCode Distance Debug ===");
        $this->info("Origin ZipCode: {$originZip}");

        // Check if origin zipcode exists in database
        $origin = ZipCodeModel::where('zip_code', $originZip)->first();
        if (!$origin) {
            $this->error("Origin zipcode {$originZip} not found in database!");
            return 1;
        }

        $this->info("Origin Coordinates: Lat={$origin->lat}, Lng={$origin->lng}");

        if ($targetZip) {
            // Debug specific zipcode pair
            $this->info("Target ZipCode: {$targetZip}");
            
            $result = $zipService->debugZipCodeDistance($originZip, $targetZip);
            
            if (isset($result['error'])) {
                $this->error("Error: " . $result['error']);
                return 1;
            }

            $this->table(
                ['Property', 'Value'],
                [
                    ['Origin ZipCode', $result['origin_zip']],
                    ['Origin Lat', $result['origin_coordinates']['lat']],
                    ['Origin Lng', $result['origin_coordinates']['lng']],
                    ['Target ZipCode', $result['target_zip']],
                    ['Target Lat', $result['target_coordinates']['lat']],
                    ['Target Lng', $result['target_coordinates']['lng']],
                    ['Distance (miles)', $result['distance_miles']],
                    ['Within 50 miles?', $result['within_50_miles'] ? 'YES' : 'NO']
                ]
            );
        } else {
            // Get all zipcodes within 50 miles
            $this->info("Finding all zipcodes within 50 miles of {$originZip}...");
            
            $zipCodes = $zipService->getZipCodesInRadiusRaw($originZip, 50);
            
            $this->info("Found " . count($zipCodes) . " zipcodes within 50 miles:");
            
            if (count($zipCodes) > 0) {
                // Show first 20 results
                $displayCodes = array_slice($zipCodes, 0, 20);
                $this->info("First 20 results: " . implode(', ', $displayCodes));
                
                if (count($zipCodes) > 20) {
                    $this->info("... and " . (count($zipCodes) - 20) . " more");
                }

                // Check if 38632 is in the results (the problematic zipcode)
                if (in_array('38632', $zipCodes)) {
                    $this->warn("WARNING: ZipCode 38632 found in results!");
                    $debugResult = $zipService->debugZipCodeDistance($originZip, '38632');
                    $this->info("Distance to 38632: " . $debugResult['distance_miles'] . " miles");
                }
            } else {
                $this->warn("No zipcodes found within 50 miles!");
            }
        }

        return 0;
    }
}
