<?php

namespace App\Services\ZipCode;

use App\Models\ZipCode as ZipCodeModel;

class ZipCode
{
    public function getZipCodesInRadiusRaw(string $zip, float $radius = 10): array
    {
        $origin = ZipCodeModel::where('zip_code', $zip)->first();
        if (!$origin || !$origin->lat || !$origin->lng) {
            \Log::warning("ZipCode::getZipCodesInRadiusRaw - Origin zipcode not found or missing coordinates", [
                'zip_code' => $zip,
                'origin_found' => $origin ? 'yes' : 'no',
                'has_lat' => $origin ? ($origin->lat ? 'yes' : 'no') : 'n/a',
                'has_lng' => $origin ? ($origin->lng ? 'yes' : 'no') : 'n/a'
            ]);
            return [];
        }

        $lat = (float)$origin->lat;
        $lng = (float)$origin->lng;

        // Log search parameters for debugging if needed
        \Log::debug("ZipCode::getZipCodesInRadiusRaw - Starting search", [
            'origin_zip' => $zip,
            'origin_lat' => $lat,
            'origin_lng' => $lng,
            'radius_miles' => $radius
        ]);

        $results = \DB::table('zip_codes')
            ->select('zip_code')
            ->selectRaw("(
                3959 * acos(
                    cos(radians(?)) *
                    cos(radians(lat)) *
                    cos(radians(lng) - radians(?)) +
                    sin(radians(?)) *
                    sin(radians(lat))
                )
            ) AS distance", [$lat, $lng, $lat])
            ->having('distance', '<=', $radius)
            ->orderBy('distance')
            ->get();

        $zipCodes = $results->pluck('zip_code')->toArray();

        \Log::debug("ZipCode::getZipCodesInRadiusRaw - Search completed", [
            'origin_zip' => $zip,
            'radius_miles' => $radius,
            'found_count' => count($zipCodes)
        ]);

        return $zipCodes;
    }

    /**
     * Get zipcodes within radius with distance information
     */
    public function getZipCodesInRadiusWithDistance(string $zip, float $radius = 10): array
    {
        $origin = ZipCodeModel::where('zip_code', $zip)->first();
        if (!$origin || !$origin->lat || !$origin->lng) {
            \Log::warning("ZipCode::getZipCodesInRadiusWithDistance - Origin zipcode not found or missing coordinates", [
                'zip_code' => $zip,
                'origin_found' => $origin ? 'yes' : 'no',
                'has_lat' => $origin ? ($origin->lat ? 'yes' : 'no') : 'n/a',
                'has_lng' => $origin ? ($origin->lng ? 'yes' : 'no') : 'n/a'
            ]);
            return [];
        }

        $lat = (float)$origin->lat;
        $lng = (float)$origin->lng;

        \Log::debug("ZipCode::getZipCodesInRadiusWithDistance - Starting search", [
            'origin_zip' => $zip,
            'origin_lat' => $lat,
            'origin_lng' => $lng,
            'radius_miles' => $radius
        ]);

        $results = \DB::table('zip_codes')
            ->select('zip_code')
            ->selectRaw("(
                3959 * acos(
                    cos(radians(?)) *
                    cos(radians(lat)) *
                    cos(radians(lng) - radians(?)) +
                    sin(radians(?)) *
                    sin(radians(lat))
                )
            ) AS distance", [$lat, $lng, $lat])
            ->having('distance', '<=', $radius)
            ->orderBy('distance')
            ->get();

        // Create associative array with zipcode as key and distance as value
        $zipCodesWithDistance = [];
        foreach ($results as $result) {
            $zipCodesWithDistance[$result->zip_code] = (float)$result->distance;
        }

        \Log::debug("ZipCode::getZipCodesInRadiusWithDistance - Search completed", [
            'origin_zip' => $zip,
            'radius_miles' => $radius,
            'found_count' => count($zipCodesWithDistance)
        ]);

        return $zipCodesWithDistance;
    }

    /**
     * Debug method to check specific zipcode coordinates and calculate distance
     */
    public function debugZipCodeDistance(string $originZip, string $targetZip): array
    {
        $origin = ZipCodeModel::where('zip_code', $originZip)->first();
        $target = ZipCodeModel::where('zip_code', $targetZip)->first();

        if (!$origin || !$target) {
            return [
                'error' => 'One or both zipcodes not found',
                'origin_found' => $origin ? true : false,
                'target_found' => $target ? true : false
            ];
        }

        $originLat = (float)$origin->lat;
        $originLng = (float)$origin->lng;
        $targetLat = (float)$target->lat;
        $targetLng = (float)$target->lng;

        // Calculate distance using haversine formula
        $distance = $this->calculateDistance($originLat, $originLng, $targetLat, $targetLng);

        return [
            'origin_zip' => $originZip,
            'origin_coordinates' => ['lat' => $originLat, 'lng' => $originLng],
            'target_zip' => $targetZip,
            'target_coordinates' => ['lat' => $targetLat, 'lng' => $targetLng],
            'distance_miles' => round($distance, 2),
            'within_50_miles' => $distance <= 50
        ];
    }

    /**
     * Calculate distance between two points using haversine formula
     */
    private function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        $earthRadius = 3959; // miles

        $dLat = deg2rad($lat2 - $lat1);
        $dLng = deg2rad($lng2 - $lng1);

        $a = sin($dLat/2) * sin($dLat/2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLng/2) * sin($dLng/2);

        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
        $distance = $earthRadius * $c;

        return $distance;
    }
}